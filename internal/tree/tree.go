// Package tree provides DecisionTree container for Mulberri decision tree implementation.
//
// Architecture:
//   - tree.go: DecisionTree container with metadata and persistence
//   - TreeMetadata: Training parameters and tree statistics
//   - JSON serialization: Complete tree save/load functionality
//
// Design Principles:
//   - DecisionTree acts as container for root node and metadata
//   - Automatic statistics calculation and maintenance
//   - JSON persistence with complete tree structure preservation
//   - Immutable metadata after construction with update capabilities
//
// Memory Management:
//   - Tree stores single root node reference plus lightweight metadata
//   - Node tree structure handled by Node implementations
//   - Metadata cached and updated on demand for performance
//
// Example:
//
//	// Create tree with trained root node
//	metadata := NewTreeMetadata("C4.5", 10, 20)
//	tree := NewDecisionTree(rootNode, features, classes, metadata)
//
//	// Save tree to file
//	tree.SaveTreeToFile("model.json")
//
//	// Load tree from file
//	loadedTree := LoadTreeFromFile("model.json")
//
// Security: No sensitive data storage, validates tree consistency.
// Performance: O(1) tree operations, O(log depth) prediction traversal.
// Dependencies: Node interface, features package, logger, JSON marshaling.
package tree

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// ====================
// Tree Metadata
// ====================

// TreeMetadata contains comprehensive information about decision tree training and structure.
//
// Stores training parameters, algorithm details, tree statistics, and creation timestamp.
// Provides complete context for tree evaluation, debugging, and model management.
//
// Constraints:
//   - Version must be non-empty for tracking compatibility
//   - Algorithm should specify C4.5 or future algorithm variants
//   - MaxDepth and MinSamples must be positive for valid training constraints
//   - Node counts automatically calculated and maintained for accuracy
//
// Security: Contains training configuration but no sensitive training data.
// Performance: Lightweight metadata structure with O(1) access to all fields.
// Relationships: Used by DecisionTree for complete model documentation.
// Side effects: Immutable after creation, statistics updated via tree operations.
type TreeMetadata struct {
	Version         string    `json:"version"`          // Model version for compatibility tracking
	CreatedAt       time.Time `json:"created_at"`       // Tree creation timestamp
	Algorithm       string    `json:"algorithm"`        // Training algorithm used (C4.5)
	MaxDepth        int       `json:"max_depth"`        // Maximum tree depth constraint from training
	MinSamples      int       `json:"min_samples"`      // Minimum samples required for split from training
	Criterion       string    `json:"criterion"`        // Split criterion used (entropy, gini)
	TotalNodes      int       `json:"total_nodes"`      // Total nodes in tree (calculated)
	LeafNodes       int       `json:"leaf_nodes"`       // Number of leaf nodes (calculated)
	TrainingSamples int       `json:"training_samples"` // Number of training instances used
}

// ====================
// Version String Generation
// ====================

// generateVersionString creates a descriptive version string indicating tree capabilities.
//
// Args:
// - binarySplitsOnly: Whether the tree was built with binary splits only
//
// Returns version string describing split behavior: "binary-only" for trees that use
// only binary splits, or "n-ary" for trees supporting multi-way categorical splits.
//
// Performance: O(1) constant time version string generation.
// Relationships: Used by NewTreeMetadata for automatic version assignment.
// Side effects: None, pure function returning descriptive version identifier.
//
// Example: version := generateVersionString(false) // Returns "n-ary"
func generateVersionString(binarySplitsOnly bool) string {
	if binarySplitsOnly {
		// Binary splits for all feature types:
		// - Numerical: threshold-based binary splits (≤ vs >)
		// - Categorical: exact match binary splits (= vs ≠)
		return "binary-only"
	} else {
		// N-ary splits enabled for categorical features:
		// - Numerical: threshold-based binary splits (≤ vs >)
		// - Categorical: multi-way splits (one branch per category value)
		return "n-ary"
	}
}

// AnalyzeTreeStructure examines tree structure and returns descriptive version string.
//
// Args:
//   - root: Root node of tree to analyze for split patterns
//
// Returns version string describing actual tree structure: "binary-only" if all
// decision nodes use binary splits, "n-ary" if any node has more than 2 children.
// Traverses entire tree to analyze split behavior and branching patterns.
//
// Performance: O(tree_size) for complete tree traversal and analysis.
// Relationships: Analyzes Node structure and child count patterns.
// Side effects: None, read-only tree structure analysis.
//
// Example: version := AnalyzeTreeStructure(tree.Root)
func AnalyzeTreeStructure(root Node) string {
	if root == nil {
		return "binary-only"
	}

	// Check if this is a decision node with children
	if decisionNode, ok := root.(*DecisionNode); ok {
		childCount := len(decisionNode.Children)

		// If any decision node has more than 2 children, it's n-ary
		if childCount > 2 {
			return "n-ary"
		}

		// Recursively check all children
		for _, child := range decisionNode.Children {
			if AnalyzeTreeStructure(child) == "n-ary" {
				return "n-ary"
			}
		}
	}

	return "binary-only"
}

// ====================
// TreeMetadata Constructor
// ====================

// NewTreeMetadata creates TreeMetadata with specified training parameters.
//
// Args:
//   - algorithm: Training algorithm identifier (typically "C4.5")
//   - maxDepth: Maximum tree depth constraint used during training
//   - minSamples: Minimum samples required for node splitting during training
//   - criterion: Split criterion used for node evaluation (entropy, gini)
//   - trainingSamples: Number of training instances used to build tree
//   - binarySplitsOnly: Whether tree was built with binary splits only
//
// Returns new TreeMetadata with current timestamp and default statistics.
// Constraints: algorithm non-empty, maxDepth/minSamples positive, trainingSamples >= 0.
// Performance: O(1) creation with timestamp generation.
// Relationships: Created during tree construction with training configuration.
// Side effects: Records current timestamp, initializes statistics to zero for later update.
//
// Example: metadata := NewTreeMetadata("C4.5", 10, 20, "entropy", 1000, true)
func NewTreeMetadata(algorithm string, maxDepth int, minSamples int, criterion string, trainingSamples int, binarySplitsOnly bool) *TreeMetadata {
	if algorithm == "" {
		logger.Error("algorithm cannot be empty")
		return nil
	}
	if maxDepth <= 0 {
		logger.Error("maxDepth must be positive")
		return nil
	}
	if minSamples <= 0 {
		logger.Error("minSamples must be positive")
		return nil
	}
	if trainingSamples < 0 {
		logger.Error("trainingSamples cannot be negative")
		return nil
	}

	return &TreeMetadata{
		Version:         generateVersionString(binarySplitsOnly),
		CreatedAt:       time.Now(),
		Algorithm:       algorithm,
		MaxDepth:        maxDepth,
		MinSamples:      minSamples,
		Criterion:       criterion,
		TotalNodes:      0, // Will be calculated by tree
		LeafNodes:       0, // Will be calculated by tree
		TrainingSamples: trainingSamples,
	}
}

// ====================
// DecisionTree Container
// ====================

// DecisionTree represents complete decision tree with root node and comprehensive metadata.
//
// Acts as container for trained tree structure with feature information, target classes,
// and training metadata. Provides tree-level operations including persistence, validation,
// and statistics management.
//
// Constraints:
//   - Root must be valid Node implementation for functional tree
//   - Features must match those used in training dataset
//   - Classes must represent all possible target values
//   - Metadata must contain valid training configuration and statistics
//
// Security: Contains tree structure and metadata but no raw training data.
// Performance: O(1) access to tree components, O(tree_size) for persistence operations.
// Relationships: Contains root Node, Feature definitions, and TreeMetadata.
// Side effects: Maintains tree statistics, handles JSON serialization lifecycle.
type DecisionTree struct {
	Root     Node          `json:"-"`        // Root node (not directly serialized)
	Features []*Feature    `json:"features"` // Feature definitions used in tree
	Classes  []string      `json:"classes"`  // Target class values
	Metadata *TreeMetadata `json:"metadata"` // Training parameters and tree statistics

	// Serialization support
	SerializableRoot *SerializableNode `json:"root"` // For JSON serialization
}

// NewDecisionTree creates DecisionTree with root node and metadata.
//
// Args:
//   - root: Trained root node of decision tree (must be valid Node implementation)
//   - features: Feature definitions used in training (must match training dataset)
//   - classes: Target class values from training data
//   - metadata: Training configuration and parameters
//
// Returns new DecisionTree with calculated statistics or nil for invalid input.
// Constraints: root must be valid, features/classes non-empty, metadata valid.
// Performance: O(tree_size) for initial statistics calculation.
// Relationships: Combines trained node structure with training metadata.
// Side effects: Calculates and caches tree statistics in metadata.
//
// Example: tree := NewDecisionTree(rootNode, featureList, classList, metadata)
func NewDecisionTree(root Node, features []*Feature, classes []string, metadata *TreeMetadata) *DecisionTree {
	if root == nil {
		logger.Error("decision tree requires a root node")
		return nil
	}
	if len(features) == 0 {
		logger.Error("decision tree requires feature definitions")
		return nil
	}
	if len(classes) == 0 {
		logger.Error("decision tree requires target classes")
		return nil
	}
	if metadata == nil {
		logger.Error("decision tree requires metadata")
		return nil
	}
	if !root.Validate() {
		logger.Error("root node validation failed")
		return nil
	}

	tree := &DecisionTree{
		Root:     root,
		Features: features,
		Classes:  classes,
		Metadata: metadata,
	}

	// Calculate and update tree statistics
	tree.updateStatistics()

	return tree
}

// updateStatistics calculates current tree statistics and updates metadata.
//
// Traverses entire tree to count total nodes, leaf nodes, and calculate tree depth.
// Updates metadata fields with current statistics for accurate tree information.
// Performance: O(tree_size) for complete tree traversal and counting.
// Relationships: Called during tree construction and after tree modifications.
// Side effects: Modifies metadata statistics fields with current tree state.
func (dt *DecisionTree) updateStatistics() {
	if dt.Root == nil {
		dt.Metadata.TotalNodes = 0
		dt.Metadata.LeafNodes = 0
		return
	}

	dt.Metadata.TotalNodes = CountNodes(dt.Root)
	dt.Metadata.LeafNodes = CountLeaves(dt.Root)
}

// GetDepth calculates maximum depth of decision tree.
//
// Returns maximum path length from root to any leaf node.
// Performance: O(tree_size) for complete depth-first traversal.
// Relationships: Uses recursive tree structure for depth calculation.
// Side effects: None, read-only tree traversal operation.
//
// Example: depth := tree.GetDepth()
func (dt *DecisionTree) GetDepth() int {
	return calculateDepth(dt.Root)
}

// calculateDepth recursively calculates tree depth from given node.
//
// Args:
//   - node: Current node in tree traversal (can be leaf or decision node)
//
// Returns maximum depth from this node to any leaf in subtree.
// Performance: O(subtree_size) for recursive traversal.
// Relationships: Helper function for GetDepth, used recursively.
// Side effects: None, read-only recursive tree traversal.
func calculateDepth(node Node) int {
	if node == nil || node.IsLeaf() {
		return 0
	}

	maxChildDepth := 0
	decisionNode := node.(*DecisionNode)
	for _, child := range decisionNode.Children {
		childDepth := calculateDepth(child)
		if childDepth > maxChildDepth {
			maxChildDepth = childDepth
		}
	}

	return maxChildDepth + 1
}

// Predict makes prediction for single instance using tree traversal.
//
// Args:
//   - instance: Feature values for prediction (map of feature_name -> value)
//
// Returns predicted class value by traversing tree from root to leaf.
// Constraints: instance must contain values for features used in tree splits.
// Performance: O(tree_depth) for single prediction via tree traversal.
// Relationships: Delegates to root node's Predict method for actual traversal.
// Side effects: None, read-only tree traversal for prediction.
//
// Example: prediction := tree.Predict(map[string]interface{}{"age": 25, "salary": 50000})
func (dt *DecisionTree) Predict(instance map[string]interface{}) interface{} {
	if dt.Root == nil {
		logger.Error("cannot predict with nil root node")
		return nil
	}

	prediction, _, _ := dt.Root.Predict(instance)
	return prediction
}

// Validate checks complete tree consistency including root, features, and metadata.
//
// Returns true if tree is valid and ready for predictions, false otherwise.
// Performance: O(tree_size) for complete tree validation including all nodes.
// Relationships: Validates root node and all children recursively.
// Side effects: Logs validation errors for debugging, read-only validation.
func (dt *DecisionTree) Validate() bool {
	if dt.Root == nil {
		logger.Error("decision tree must have a root node")
		return false
	}
	if len(dt.Features) == 0 {
		logger.Error("decision tree must have feature definitions")
		return false
	}
	if len(dt.Classes) == 0 {
		logger.Error("decision tree must have target classes")
		return false
	}
	if dt.Metadata == nil {
		logger.Error("decision tree must have metadata")
		return false
	}

	// Validate root node and entire tree structure
	if !dt.Root.Validate() {
		logger.Error("root node validation failed")
		return false
	}

	return true
}

// ====================
// JSON Serialization Support
// ====================

// MarshalJSON implements custom JSON marshaling for DecisionTree.
//
// Converts root node to SerializableNode format and marshals complete tree structure.
// Preserves all tree information including node hierarchy and metadata.
// Performance: O(tree_size) for complete tree serialization.
// Relationships: Uses root node's ToSerializable() for node tree conversion.
// Side effects: Updates SerializableRoot field for JSON output.
func (dt *DecisionTree) MarshalJSON() ([]byte, error) {
	// Convert root to serializable format for JSON compatibility
	if dt.Root != nil {
		dt.SerializableRoot = dt.Root.ToSerializable()
	}

	// Create temporary struct for marshaling to avoid infinite recursion
	type TreeAlias DecisionTree
	return json.MarshalIndent((*TreeAlias)(dt), "", "  ")
}

// UnmarshalJSON implements custom JSON unmarshaling for DecisionTree.
//
// Reconstructs tree structure from JSON including root node conversion from SerializableNode.
// Restores complete tree functionality from persisted JSON representation.
// Performance: O(tree_size) for complete tree reconstruction.
// Relationships: Uses FromSerializable() to convert serialized nodes back to Node interface.
// Side effects: Reconstructs root Node from SerializableRoot, validates tree consistency.
func (dt *DecisionTree) UnmarshalJSON(data []byte) error {
	// Create temporary struct for unmarshaling to avoid infinite recursion
	type TreeAlias DecisionTree
	aux := (*TreeAlias)(dt)

	if err := json.Unmarshal(data, aux); err != nil {
		logger.Error(fmt.Sprintf("failed to unmarshal tree JSON: %v", err))
		return err
	}

	// Convert serializable root back to Node interface
	if dt.SerializableRoot != nil {
		root, err := FromSerializable(dt.SerializableRoot)
		if err != nil {
			logger.Error(fmt.Sprintf("failed to deserialize root node: %v", err))
			return fmt.Errorf("invalid root node in JSON: %w", err)
		}
		dt.Root = root
	}

	// Validate reconstructed tree
	if !dt.Validate() {
		logger.Error("deserialized tree validation failed")
		return fmt.Errorf("invalid tree structure in JSON")
	}

	return nil
}

// ====================
// Tree Persistence
// ====================

// SaveTreeToFile saves complete decision tree to JSON file.
//
// Args:
//   - filename: Output file path for JSON tree serialization
//
// Returns nil on success, error on file operation failures.
// Constraints: filename must be writable path with appropriate permissions.
// Performance: O(tree_size) for serialization plus file I/O overhead.
// Relationships: Uses MarshalJSON for tree serialization before file writing.
// Side effects: Creates or overwrites file at specified path with tree JSON.
//
// Example: tree.SaveTreeToFile("trained_model.json")
func (dt *DecisionTree) SaveTreeToFile(filename string) error {
	if filename == "" {
		logger.Error("filename cannot be empty")
		return fmt.Errorf("invalid filename")
	}

	// Validate tree before saving
	if !dt.Validate() {
		logger.Error("cannot save invalid tree")
		return fmt.Errorf("tree validation failed")
	}

	// Marshal tree to JSON
	data, err := json.MarshalIndent(dt, "", "  ")
	if err != nil {
		logger.Error(fmt.Sprintf("failed to marshal tree: %v", err))
		return fmt.Errorf("JSON serialization failed: %w", err)
	}

	// Write to file
	if err := os.WriteFile(filename, data, 0644); err != nil {
		logger.Error(fmt.Sprintf("failed to write tree file: %v", err))
		return fmt.Errorf("file write failed: %w", err)
	}

	logger.Info(fmt.Sprintf("tree saved to %s (%d nodes, %d leaves)",
		filename, dt.Metadata.TotalNodes, dt.Metadata.LeafNodes))
	return nil
}

// LoadTreeFromFile loads decision tree from JSON file.
//
// Args:
//   - filename: Input file path containing JSON tree serialization
//
// Returns loaded DecisionTree or nil for invalid file or tree data.
// Constraints: file must exist and contain valid JSON tree structure.
// Performance: O(tree_size) for deserialization plus file I/O overhead.
// Relationships: Uses UnmarshalJSON for tree reconstruction from file data.
// Side effects: Logs errors for file or deserialization failures.
//
// Example: tree := LoadTreeFromFile("trained_model.json")
func LoadTreeFromFile(filename string) *DecisionTree {
	if filename == "" {
		logger.Error("filename cannot be empty")
		return nil
	}

	// Read file
	data, err := os.ReadFile(filename)
	if err != nil {
		logger.Error(fmt.Sprintf("failed to read tree file %s: %v", filename, err))
		return nil
	}

	// Unmarshal JSON to tree
	var tree DecisionTree
	if err := json.Unmarshal(data, &tree); err != nil {
		logger.Error(fmt.Sprintf("failed to unmarshal tree from %s: %v", filename, err))
		return nil
	}

	// Validate loaded tree
	if !tree.Validate() {
		logger.Error(fmt.Sprintf("loaded tree from %s failed validation", filename))
		return nil
	}

	logger.Info(fmt.Sprintf("tree loaded from %s (%d nodes, %d leaves)",
		filename, tree.Metadata.TotalNodes, tree.Metadata.LeafNodes))
	return &tree
}

// ====================
// Tree Statistics and Analysis
// ====================

// UpdateStatistics recalculates all tree statistics and updates metadata.
//
// Traverses tree to count nodes, leaves, and other structural metrics.
// Updates metadata with current tree state for accurate statistics.
// Performance: O(tree_size) for complete tree analysis.
// Relationships: Uses CountNodes and CountLeaves utility functions.
// Side effects: Modifies metadata statistics to reflect current tree structure.
//
// Example: tree.UpdateStatistics() // After tree modifications
func (dt *DecisionTree) UpdateStatistics() {
	dt.updateStatistics()
}

// UpdateVersion analyzes tree structure and updates version string to reflect actual split behavior.
//
// Examines the tree's decision nodes to determine if it uses binary-only splits or
// supports n-ary (multi-way) splits, then updates the metadata version accordingly.
// Useful for trees loaded from files or modified after creation.
//
// Performance: O(tree_size) for complete tree structure analysis.
// Relationships: Uses AnalyzeTreeStructure to examine split patterns.
// Side effects: Modifies metadata version field to reflect actual tree structure.
//
// Example: tree.UpdateVersion() // After loading tree from file
func (dt *DecisionTree) UpdateVersion() {
	if dt.Metadata != nil {
		dt.Metadata.Version = AnalyzeTreeStructure(dt.Root)
	}
}

// GetTreeSummary returns formatted string with tree statistics and metadata.
//
// Returns comprehensive tree information including structure statistics,
// training parameters, and model identification for debugging and analysis.
// Performance: O(tree_depth) for depth calculation plus O(1) for other statistics.
// Relationships: Uses tree statistics and metadata for summary generation.
// Side effects: None, read-only access to tree information.
//
// Example: summary := tree.GetTreeSummary()
func (dt *DecisionTree) GetTreeSummary() string {
	if dt.Root == nil {
		return "Empty Decision Tree"
	}

	return fmt.Sprintf(`Decision Tree Summary:
  Algorithm: %s
  Features: %d
  Classes: %d (%v)
  Total Nodes: %d
  Leaf Nodes: %d
  Tree Depth: %d
  Training Samples: %d
  Max Depth Setting: %d
  Min Samples Setting: %d
  Criterion: %s
  Created: %s`,
		dt.Metadata.Algorithm,
		len(dt.Features),
		len(dt.Classes), dt.Classes,
		dt.Metadata.TotalNodes,
		dt.Metadata.LeafNodes,
		dt.GetDepth(),
		dt.Metadata.TrainingSamples,
		dt.Metadata.MaxDepth,
		dt.Metadata.MinSamples,
		dt.Metadata.Criterion,
		dt.Metadata.CreatedAt.Format("2006-01-02 15:04:05"))
}

// ====================
// Feature and Class Management
// ====================

// GetFeature retrieves feature definition by name.
//
// Args:
//   - name: Feature name to lookup in tree's feature definitions
//
// Returns Feature definition or nil if feature name not found in tree.
// Performance: O(f) where f = number of features for linear search.
// Relationships: Searches tree's feature definitions for specified feature.
// Side effects: None, read-only search through feature list.
//
// Example: feature := tree.GetFeature("age")
func (dt *DecisionTree) GetFeature(name string) *Feature {
	for _, feature := range dt.Features {
		if feature.Name == name {
			return feature
		}
	}
	logger.Error(fmt.Sprintf("feature %s not found in tree", name))
	return nil
}

// HasClass checks if target class exists in tree's class definitions.
//
// Args:
//   - class: Target class value to check for existence
//
// Returns true if class is valid target for this tree, false otherwise.
// Performance: O(c) where c = number of classes for linear search.
// Relationships: Checks against tree's defined target classes.
// Side effects: None, read-only search through class list.
//
// Example: isValid := tree.HasClass("approved")
func (dt *DecisionTree) HasClass(class string) bool {
	for _, c := range dt.Classes {
		if c == class {
			return true
		}
	}
	return false
}

// GetFeatureNames returns slice of all feature names used in tree.
//
// Returns feature names in same order as Features slice for consistent iteration.
// Performance: O(f) where f = number of features for slice creation.
// Relationships: Extracts names from tree's feature definitions.
// Side effects: Creates new slice, original Features slice unchanged.
func (dt *DecisionTree) GetFeatureNames() []string {
	names := make([]string, len(dt.Features))
	for i, feature := range dt.Features {
		names[i] = feature.Name
	}
	return names
}
