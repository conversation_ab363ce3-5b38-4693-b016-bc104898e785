package training

import (
	"math"
	"testing"

	data "github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
)

// buildTestDataset creates a small dataset for split evaluation tests
func buildTestDataset() *data.Dataset[string] {
	d := data.NewDataset[string](0)

	// Features
	d.AddIntColumn("age", []int64{25, 30, 35, 40}, []bool{false, false, false, false})
	d.AddStringColumn("education", []string{"college", "high_school", "college", "graduate"}, []bool{false, false, false, false})

	// Targets: 1x "A", 3x "B"
	d.AddTarget("A")
	d.AddTarget("B")
	d.AddTarget("B")
	d.AddTarget("B")

	return d
}

func TestEvaluateFeatureSplits_Numerical(t *testing.T) {
	d := buildTestDataset()
	view := d.<PERSON><PERSON><PERSON>iew([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "age", true)
	if res == nil {
		t.Fatalf("expected non-nil result")
	}

	if res.FeatureName != "age" || res.FeatureType != features.IntegerFeature {
		t.Fatalf("unexpected feature metadata: %s %v", res.FeatureName, res.FeatureType)
	}

	// Base impurity for targets [A,B,B,B]
	if math.Abs(res.BaseImpurity-0.8112781244591328) > 1e-9 {
		t.Errorf("unexpected base impurity: %v", res.BaseImpurity)
	}

	if res.BestCandidate == nil {
		t.Fatalf("expected a best candidate for age")
	}

	if res.BestCandidate.Type != NumericalSplit {
		t.Fatalf("expected numerical split, got %v", res.BestCandidate.Type)
	}

	if res.BestCandidate.Threshold == nil || math.Abs(*res.BestCandidate.Threshold-27.5) > 1e-6 {
		t.Errorf("expected threshold ~27.5, got %v", res.BestCandidate.Threshold)
	}

	if res.BestCandidate.LeftSize != 1 || res.BestCandidate.RightSize != 3 {
		t.Errorf("unexpected child sizes: L=%d R=%d", res.BestCandidate.LeftSize, res.BestCandidate.RightSize)
	}

	if res.BestCandidate.Gain <= 0 {
		t.Errorf("expected positive gain, got %v", res.BestCandidate.Gain)
	}
}

func TestEvaluateFeatureSplits_Categorical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "education", true)
	if res == nil {
		t.Fatalf("expected non-nil result")
	}

	if res.FeatureType != features.StringFeature {
		t.Fatalf("expected string feature type, got %v", res.FeatureType)
	}

	if res.BestCandidate == nil || res.BestCandidate.Type != CategoricalSplit {
		t.Fatalf("expected categorical best candidate")
	}

	// Expect split on "college" vs others
	if res.BestCandidate.Value != "college" {
		t.Errorf("expected best value 'college', got %v", res.BestCandidate.Value)
	}

	if res.BestCandidate.LeftSize != 2 || res.BestCandidate.RightSize != 2 {
		t.Errorf("unexpected child sizes: %d/%d", res.BestCandidate.LeftSize, res.BestCandidate.RightSize)
	}

	if res.BestCandidate.Gain <= 0 {
		t.Errorf("expected positive gain, got %v", res.BestCandidate.Gain)
	}
}

func TestEvaluateAllFeatureSplits_SelectsBest(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Include one missing feature to exercise skip path
	res := EvaluateAllFeatureSplits(view, d, []string{"missing", "age", "education"}, true)
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("expected a best overall result")
	}

	// Age split should have higher gain than education for this dataset
	if res.FeatureName != "age" {
		t.Errorf("expected best feature 'age', got %s", res.FeatureName)
	}
}

func TestApplySplitToView_Numerical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "age", true)
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("need a split candidate")
	}

	left, right := ApplySplitToView(view, d, res.BestCandidate)
	if left == nil || right == nil {
		t.Fatalf("expected non-nil child views")
	}

	if left.GetSize() != res.BestCandidate.LeftSize || right.GetSize() != res.BestCandidate.RightSize {
		t.Errorf("child sizes mismatch: got %d/%d want %d/%d", left.GetSize(), right.GetSize(), res.BestCandidate.LeftSize, res.BestCandidate.RightSize)
	}
}

func TestApplySplitToView_Categorical(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	res := EvaluateFeatureSplits(view, d, "education", true)
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("need a split candidate")
	}

	left, right := ApplySplitToView(view, d, res.BestCandidate)
	if left == nil || right == nil {
		t.Fatalf("expected non-nil child views")
	}

	if left.GetSize() != 2 || right.GetSize() != 2 {
		t.Errorf("unexpected sizes: %d/%d", left.GetSize(), right.GetSize())
	}
}

func TestGetSplitDescriptionAndExtractHelpers(t *testing.T) {
	thr := 10.0
	num := &SplitCandidate{FeatureName: "age", Type: NumericalSplit, Threshold: &thr}
	if desc := GetSplitDescription(num); desc != "age <= 10.000" {
		t.Errorf("unexpected numerical desc: %s", desc)
	}

	cat := &SplitCandidate{FeatureName: "education", Type: CategoricalSplit, Value: "college"}
	if desc := GetSplitDescription(cat); desc != "education == 'college'" {
		t.Errorf("unexpected categorical desc: %s", desc)
	}

	if desc := GetSplitDescription(nil); desc != "no split" {
		t.Errorf("unexpected nil desc: %s", desc)
	}

	// extract helpers
	i := int64(7)
	if v := extractNumericalValue(&i); v == nil || *v != 7.0 {
		t.Errorf("extractNumericalValue int64 failed: %v", v)
	}
	f := 3.25
	if v := extractNumericalValue(&f); v == nil || *v != 3.25 {
		t.Errorf("extractNumericalValue float64 failed: %v", v)
	}
	if v := extractNumericalValue("nope"); v != nil {
		t.Errorf("extractNumericalValue should be nil for non-numeric, got %v", v)
	}

	s := "hi"
	if v := extractStringValue(&s); v == nil || *v != "hi" {
		t.Errorf("extractStringValue failed: %v", v)
	}
	if v := extractStringValue(&i); v != nil {
		t.Errorf("extractStringValue should be nil for non-string, got %v", v)
	}
}

func TestEvaluateFeatureSplits_InvalidFeature(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	if res := EvaluateFeatureSplits(view, d, "does_not_exist", true); res != nil {
		t.Errorf("expected nil result for missing feature, got %+v", res)
	}
}

func TestEvaluateAllFeatureSplits_NoneValid(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	if res := EvaluateAllFeatureSplits(view, d, []string{"missing1", "missing2"}, true); res != nil {
		t.Errorf("expected nil when no features are valid, got %+v", res)
	}
}

func TestNAryCategoricalSplitEvaluator(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Test n-ary categorical split evaluation
	res := EvaluateFeatureSplits(view, d, "education", false) // Enable n-ary splits
	if res == nil {
		t.Fatalf("expected non-nil result for n-ary evaluation")
	}

	if res.FeatureType != features.StringFeature {
		t.Errorf("expected StringFeature, got %v", res.FeatureType)
	}

	if res.BestCandidate == nil {
		t.Fatalf("expected non-nil best candidate")
	}

	// N-ary split should have NAryCategoricalSplit type
	if res.BestCandidate.Type != NAryCategoricalSplit {
		t.Errorf("expected NAryCategoricalSplit, got %v", res.BestCandidate.Type)
	}

	// Should have ChildPartitions populated
	if res.BestCandidate.ChildPartitions == nil {
		t.Errorf("expected non-nil ChildPartitions for n-ary split")
	}

	// Should have multiple partitions (more than 2 for n-ary)
	if len(res.BestCandidate.ChildPartitions) < 2 {
		t.Errorf("expected at least 2 child partitions, got %d", len(res.BestCandidate.ChildPartitions))
	}

	// Gain should be positive
	if res.BestCandidate.Gain <= 0 {
		t.Errorf("expected positive gain, got %v", res.BestCandidate.Gain)
	}
}

func TestApplyNArySplitToView(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Get n-ary split candidate
	res := EvaluateFeatureSplits(view, d, "education", false)
	if res == nil || res.BestCandidate == nil {
		t.Fatalf("need an n-ary split candidate")
	}

	// Apply n-ary split
	childViews := ApplyNArySplitToView(view, d, res.BestCandidate)
	if childViews == nil {
		t.Fatalf("expected non-nil child views map")
	}

	// Should have multiple child views
	if len(childViews) < 2 {
		t.Errorf("expected at least 2 child views, got %d", len(childViews))
	}

	// Total samples across children should equal parent
	totalChildSamples := 0
	for _, childViewInterface := range childViews {
		if childView, ok := childViewInterface.(*data.DatasetView[string]); ok {
			totalChildSamples += childView.GetSize()
		}
	}

	if totalChildSamples != view.GetSize() {
		t.Errorf("child samples (%d) should equal parent samples (%d)", totalChildSamples, view.GetSize())
	}

	// Each child view should be non-empty
	for value, childViewInterface := range childViews {
		if childView, ok := childViewInterface.(*data.DatasetView[string]); ok {
			if childView.GetSize() == 0 {
				t.Errorf("child view for value %v should not be empty", value)
			}
		} else {
			t.Errorf("child view for value %v is not the correct type", value)
		}
	}
}

func TestBinaryVsNAryComparison(t *testing.T) {
	d := buildTestDataset()
	view := d.CreateView([]int{0, 1, 2, 3})

	// Compare binary vs n-ary splits for same feature
	binaryRes := EvaluateFeatureSplits(view, d, "education", true)  // Binary splits
	naryRes := EvaluateFeatureSplits(view, d, "education", false)   // N-ary splits

	if binaryRes == nil || naryRes == nil {
		t.Fatalf("both binary and n-ary evaluations should succeed")
	}

	// Both should find splits
	if binaryRes.BestCandidate == nil || naryRes.BestCandidate == nil {
		t.Fatalf("both evaluations should find split candidates")
	}

	// Types should be different
	if binaryRes.BestCandidate.Type != CategoricalSplit {
		t.Errorf("binary split should be CategoricalSplit, got %v", binaryRes.BestCandidate.Type)
	}

	if naryRes.BestCandidate.Type != NAryCategoricalSplit {
		t.Errorf("n-ary split should be NAryCategoricalSplit, got %v", naryRes.BestCandidate.Type)
	}

	// N-ary should have ChildPartitions, binary should not
	if binaryRes.BestCandidate.ChildPartitions != nil {
		t.Errorf("binary split should not have ChildPartitions")
	}

	if naryRes.BestCandidate.ChildPartitions == nil {
		t.Errorf("n-ary split should have ChildPartitions")
	}

	// Both should have positive gain
	if binaryRes.BestCandidate.Gain <= 0 {
		t.Errorf("binary split should have positive gain, got %v", binaryRes.BestCandidate.Gain)
	}

	if naryRes.BestCandidate.Gain <= 0 {
		t.Errorf("n-ary split should have positive gain, got %v", naryRes.BestCandidate.Gain)
	}
}
